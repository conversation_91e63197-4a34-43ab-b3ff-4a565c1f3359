import subprocess
import os
import shutil
import sys
import grpc
from api.netauth.v1 import card_pb2, card_pb2_grpc

version = "v10.6.2"
installer_name = f"installer_{version}."
installer_path = f"./dist/{installer_name}"
installer_name = installer_name.rstrip(".exe")
update_info = """\
- 修复 打牛有时误跑错地图回不去
- 优化 增加组队是否确保组满第五页
- 优化 组队重试时重新创队
- 优化 帮会生产功能可指定材料号名字
- 修复 跑动遇敌算法容易跑偏到角落
- 修复 清理背包有时无限用物品不成功
"""

def build():
    with open("version.py", "w") as f:
        f.write(f'version = "{version}"')

    subprocess.run(["python", "pyd_builder.py", "build_ext", "--inplace"])

    shutil.copy("./qiling.pyd", "./biz/res/")

    subprocess.run(["pyinstaller", "qiling.spec", "--clean", "-y"])

    os.rename("./dist/qiling/qiling.exe", "./dist/qiling/opengl32ws.dll")

    subprocess.run(["pyinstaller", "launcher.spec", "--clean", "-y"])

    shutil.copy("./dist/launcher/launcher.exe", "./dist/qiling/")

    subprocess.run(["ISCC.exe", "/O+", "installer.iss"])

    os.rename("./dist/installer.exe", installer_path)




def update():
    print("正在设置最新版本...")
    installer_url = f"https://qcdn.soult.cn/{installer_name}"
    api_token = "47f19fa7-1599-4f2e-bddf-aa42cb605a3a"
    try:
        with grpc.insecure_channel('yt.soult.cn:9000') as channel:
            stub = card_pb2_grpc.CardStub(channel)
            req = card_pb2.SetLatestVersionInfoRequest()
            req.latest_version = version
            req.update_info = update_info
            req.latest_installer_url = installer_url
            req.token = api_token
            resp = stub.SetLatestVersionInfo(req)
        print(f'设置最新版本成功')
    except Exception as e:
        print(f'设置最新版本失败: {e}')


if __name__ == '__main__':
    if len(sys.argv) > 1:
        func_name = sys.argv[1]
        if func_name == 'update':
            update()
        elif func_name == 'upload':
            upload()
        elif func_name == 'build':
            build()
        else:
            print("参数错误")
